import asyncio
import logging
import os
import sys
import time
import re
import numpy as np
import ctypes
from typing import Dict, Optional
from PySide6 import QtWidgets, QtGui
from PySide6.QtCore import *
from PySide6.QtGui import QAction
from PySide6.QtWidgets import *
import qdarktheme
from ConfigParser import ConfigParser
from Logger import Logger
from UsbDevDetect import UsbDevDetect
from AdvanceSetting import AdvancedSetting
from LogIn import UserLogIn
from CsvFileOps import RecordCsvFileOps, CsvFileOps
from BaseSubMainWindow import BaseResource
from ui_CaseCtrlTool import Ui_CaseCtrlTool
from ServerRequest import ServerRequest
from RecordDb import RecordDb
from MysqlDbOps import DevTestMysqlDb
from SerialPort import SPCore
from CaseBleDevice import CaseBleDevice
from CaseParamsCfg import CaseParams, showAidsParams, structure_to_dict, json_to_aids_params

VERSION_STR = "V1.0.0"


class SyncParamsThread(QThread):
    result_signal = Signal(str, bool, list)

    def __init__(self, aidsDev, cmdStr):
        super().__init__()
        self.aidsDev = aidsDev
        self.cmdStr = cmdStr

    def run(self):
        state, response = self.aidsDev.send_command(self.cmdStr, params=None, blocking=True)
        self.result_signal.emit(self.cmdStr, state, response)


class CaseCtrlTool(UsbDevDetect):
    remoteRecordDb = None
    remoteDbConnect = False
    sync_commands = []
    devConfigParams = {}
    bandNumber = 19
    maxBandNumber = 20

    def __init__(self, parent=None):
        super().__init__()
        self.leditLocalDb = None
        self.leditRemoteDb = None
        self.ui = Ui_CaseCtrlTool()
        self.ui.setupUi(self)

        self.logger = Logger(level="info")
        self.appConfig = ConfigParser.getAppConfig("Config.ini")
        self.initAppConfig()
        self.appTitle = self.appConfig.get("App_Config", "title")

        # BLE命令响应相关
        self._cmd_timestamp = 0
        self._current_cmd = None

        self.setWindowTitle(self.appTitle + " " + VERSION_STR)

        self.logTextMenu = QMenu(self)
        self.clearItem = QAction("Clear", self)
        self.clearItem.triggered.connect(self.clearLogText)
        self.logTextMenu.addAction(self.clearItem)
        self.ui.logText.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.ui.logText.customContextMenuRequested.connect(self.createLogTextRightMenu)

        self.initUIDisplay()

        localDbName = BaseResource.getConfigFileFullName("Records.db", folder="Records")
        localDbPassword = "audio_freq_records"
        logging.info("Init Records Database in localhost!")
        self.localRecordDb = RecordDb(localDbName, localDbPassword)
        if not self.localRecordDb.initDatabase():
            logging.error("Init Local Records Database error!")
            self.localRecordDb = None
            if self.leditLocalDb:
                self.setTestResult(self.leditLocalDb, "连接异常", "fail")
        else:
            if self.leditLocalDb:
                self.setTestResult(self.leditLocalDb, "连接正常", "pass")

        self.known_devices: Dict[str, dict] = {}
        self.connected_device: Optional[dict] = None
        self._is_subscribed: bool = False
        self.current_char_handle: Optional[int] = None
        self.bleState = "Init"

        # 初始化BLE工作线程
        self.aidsDev = CaseBleDevice()
        self.aidsDev.leftOrRight = 0xAA
        self.setup_worker_connections()

        self.rmsTimer = QTimer()
        self.rmsTimer.timeout.connect(self.onRmsTimerFunc)

        QTimer.singleShot(1, self.onQApplicationStarted)

    def initAppConfig(self):
        if not self.appConfig.has_option("App_Config", "title"):
            if not self.appConfig.has_section("App_Config"):
                self.appConfig.add_section("App_Config")
            self.appConfig.set("App_Config", "title", self.windowTitle())

        if self.appConfig.has_option("Log", "level"):
            self.logger.set_level(self.appConfig.get("Log", "level"))
        else:
            if not self.appConfig.has_section("Log"):
                self.appConfig.add_section("Log")
            self.appConfig.set("Log", "level", "info")

        if not self.appConfig.has_option("UI_Config", "log_display"):
            # Mode: 0 -> disable, 1 -> enable
            if not self.appConfig.has_section("UI_Config"):
                self.appConfig.add_section("UI_Config")
            self.appConfig.set("UI_Config", "log_display", "1")

        if not self.appConfig.has_option("Setting", "record_in_server"):
            self.appConfig.set("Setting", "record_in_server", "True")

        if not self.appConfig.has_option("Setting", "last_connect_dev"):
            self.appConfig.set("Setting", "last_connect_dev", "")

        if not self.appConfig.has_section("Server"):
            self.appConfig.add_section("Server")
        if not self.appConfig.has_option("Server", "ip"):
            self.appConfig.set("Server", "ip", "************")
        if not self.appConfig.has_option("Server", "port"):
            self.appConfig.set("Server", "port", "9000")
        if not self.appConfig.has_option("Server", "user"):
            self.appConfig.set("Server", "user", "iHealth")
        if not self.appConfig.has_option("Server", "passwd"):
            self.appConfig.set("Server", "passwd", "yinkman2025")
        if not self.appConfig.has_option("Server", "model"):
            self.appConfig.set("Server", "model", "Aid100")

        self.saveSysConfig()

    def initStatusBar(self):
        font = QtGui.QFont()
        font.setFamilies(["\u5fae\u8f6f\u96c5\u9ed1"])
        font.setPointSize(10)
        widget = QWidget(self)
        widget.setLayout(QHBoxLayout())
        widget.layout().setContentsMargins(1, 1, 1, 1)
        widget.setMaximumWidth(int(self.width()))
        labelLocal = QLabel("本地数据库：", self)
        labelLocal.setAlignment(Qt.AlignmentFlag.AlignCenter)
        # labelLocal.setMinimumSize(self.width() / 3, 20)
        labelLocal.setFont(font)
        self.leditLocalDb = QLineEdit(self)
        self.leditLocalDb.setFont(font)
        self.leditLocalDb.setReadOnly(True)
        widget.layout().addWidget(labelLocal)
        widget.layout().addWidget(self.leditLocalDb)

        labelLocal = QLabel("远程数据库：", self)
        labelLocal.setAlignment(Qt.AlignmentFlag.AlignLeft)
        # labelLocal.setMinimumSize(self.width() / 3, 20)
        labelLocal.setFont(font)
        self.leditRemoteDb = QLineEdit(self)
        self.leditRemoteDb.setFont(font)
        self.leditRemoteDb.setReadOnly(True)
        widget.layout().addWidget(labelLocal)
        widget.layout().addWidget(self.leditRemoteDb)

        self.statusBar().setLayoutDirection(Qt.LayoutDirection.LeftToRight)
        self.statusBar().addWidget(widget, 1)
        self.statusBar().setFont(font)

    def initUIDisplay(self):
        self.initStatusBar()

        if (self.appConfig.getint("UI_Config", "log_display") == 0):
            self.ui.groupLog.hide()
            # self.resize(self.width(), self.height() - 150)
        elif self.ui.groupLog.isHidden():
            # self.resize(self.width(), self.height() + 150)
            self.ui.groupLog.show()

        self.ui.groupBoxYmAlg.setChecked(False)
        self.ui.groupBoxWDRC.setChecked(False)
        self.ui.btnSync.setEnabled(False)
        self.ui.btnSave.setEnabled(False)
        self.ui.btnScan.setText("开始扫描")

        if self.bandNumber > self.maxBandNumber:
            self.bandNumber = self.maxBandNumber

        # init wdrc dBSPL value: small = 50dB, medium = 65dB, large = 80dB
        for idx in range(self.maxBandNumber):
            groupBox = self.findChild(QtWidgets.QGroupBox, f'gpBand_{idx}')
            if groupBox and idx >= self.bandNumber:
                groupBox.hide()
            smallSPL = self.findChild(QtWidgets.QDoubleSpinBox, f'dSBoxSmallSPLBand_{idx}')
            mediumSPL = self.findChild(QtWidgets.QDoubleSpinBox, f'dSBoxMediumSPLBand_{idx}')
            largeSPL = self.findChild(QtWidgets.QDoubleSpinBox, f'dSBoxLargeSPLBand_{idx}')
            if smallSPL:
                smallSPL.setValue(50)
            if mediumSPL:
                mediumSPL.setValue(65)
            if largeSPL:
                largeSPL.setValue(80)

        # self.menuBar().hide()
        self.statusBar().hide()

    def initRemoteDb(self):
        if self.appConfig.getboolean("Setting", "record_in_server"):
            logging.info("Init Records Database in remote server!")
            self.remoteRecordDb = DevTestMysqlDb()
            if not self.remoteRecordDb.initDatabase():
                logging.error("Init remote Records Database error!")
                self.remoteRecordDb = None
                if self.leditRemoteDb:
                    self.setTestResult(self.leditRemoteDb, "连接异常", "fail")
            else:
                if self.leditRemoteDb:
                    self.setTestResult(self.leditRemoteDb, "连接正常", "pass")
                self.remoteDbConnect = True
        else:
            self.remoteRecordDb = None
            if self.leditRemoteDb:
                self.setTestResult(self.leditRemoteDb, "未启用", "default")

    def bindEvent(self):
        logging.info("Connect system event processing.")
        self.ui.btnAlgActivate.clicked.connect(self.onAlgActivate)
        self.ui.btnScan.clicked.connect(self.scanDevices)
        self.ui.btnConnect.clicked.connect(self.connectDevice)
        self.ui.btnSync.clicked.connect(self.onSyncParams)
        self.ui.btnSave.clicked.connect(self.onSaveParams)

        self.ui.groupBoxYmAlg.toggled.connect(self.onYmAlgEnabled)
        self.ui.groupBoxNs.toggled.connect(self.onNsEnabled)
        self.ui.comboBoxNsMode.currentIndexChanged.connect(self.onNsModeChanged)
        self.ui.groupBoxAFC.toggled.connect(self.onAfcEnabled)
        self.ui.comboBoxAFCMode.currentIndexChanged.connect(self.onAfcModeChanged)
        self.ui.doubleSpinBoxAFCSpeechLevel.valueChanged.connect(self.onAfcSpeechLevelChanged)
        self.ui.spinBoxAFCDelay.valueChanged.connect(self.onAfcDelayChanged)
        self.ui.spinBoxAFCFilterLen.valueChanged.connect(self.onAfcFilterLenChanged)
        self.ui.groupBoxBeam.toggled.connect(self.onBeamEnabled)
        self.ui.spinBoxBeamAngle.valueChanged.connect(self.onBeamAngleChanged)
        self.ui.groupBoxFreqShift.toggled.connect(self.onFreqShiftEnabled)
        self.ui.doubleSpinBoxFreqShiftLevel.valueChanged.connect(self.onFreqShiftLevelChanged)
        self.ui.doubleSpinBoxInputGain.valueChanged.connect(self.onInputGainChanged)
        self.ui.doubleSpinBoxOutputGain.valueChanged.connect(self.onOutputGainChanged)
        self.ui.groupBoxLimit.toggled.connect(self.onLimitEnabled)
        self.ui.dSBoxLimitThreshold.valueChanged.connect(self.onLimitThresholdChanged)
        self.ui.dSBoxLimitKnee.valueChanged.connect(self.onLimitKneeChanged)
        self.ui.groupBoxSweepFreq.toggled.connect(self.onSweepFreqEnabled)
        self.ui.spinBoxSweepFreq.valueChanged.connect(self.onSweepFreqChanged)
        self.ui.comboBoxSweepType.currentIndexChanged.connect(self.onSweepTypeChanged)
        self.ui.groupBoxFrameRms.toggled.connect(self.onFrameRmsEnabled)

        self.ui.comboBoxDebugLevel.currentIndexChanged.connect(self.onDebugLevelChanged)
        self.ui.comboBoxAudioDumpFormat.currentIndexChanged.connect(self.onAudioDumpChanged)
        self.ui.comboBoxPsapSwCtrl.currentIndexChanged.connect(self.onPsapSwCtrlChanged)

        self.ui.groupBoxWDRC.toggled.connect(self.onWdrcEnabled)
        self.ui.sliderAfcAllGain.valueChangedDelayed.connect(self.onSliderWdrcAllGainChanged)
        self.ui.spinAfcAllGain.valueChanged.connect(self.onSpinWdrcAllGainChanged)
        for idx in range(self.bandNumber):
            obj = self.findChild(QtWidgets.QSpinBox, 'spinBoxAttackBand_%s' % idx)
            obj.valueChanged[int].connect(self.onWdrcAttackChanged)
            obj = self.findChild(QtWidgets.QSpinBox, 'spinBoxReleaseBand_%s' % idx)
            obj.valueChanged[int].connect(self.onWdrcReleaseChanged)
            obj = self.findChild(QtWidgets.QDoubleSpinBox, 'dSBoxLimitThresholdBand_%s' % idx)
            obj.valueChanged[float].connect(self.onWdrcLimitThresholdChanged)
            obj = self.findChild(QtWidgets.QDoubleSpinBox, 'dSBoxLimitKneeBand_%s' % idx)
            obj.valueChanged[float].connect(self.onWdrcLimitKneeChanged)
            obj = self.findChild(QtWidgets.QDoubleSpinBox, 'dSBoxSmallSPLBand_%s' % idx)
            obj.valueChanged[float].connect(self.onWdrcSmallSPLChanged)
            obj = self.findChild(QtWidgets.QDoubleSpinBox, 'dSBoxSmallSPLGainBand_%s' % idx)
            obj.valueChanged[float].connect(self.onWdrcSmallSPLGainChanged)
            obj = self.findChild(QtWidgets.QDoubleSpinBox, 'dSBoxMediumSPLBand_%s' % idx)
            obj.valueChanged[float].connect(self.onWdrcMediumSPLChanged)
            obj = self.findChild(QtWidgets.QDoubleSpinBox, 'dSBoxMediumSPLGainBand_%s' % idx)
            obj.valueChanged[float].connect(self.onWdrcMediumSPLGainChanged)
            obj = self.findChild(QtWidgets.QDoubleSpinBox, 'dSBoxLargeSPLBand_%s' % idx)
            obj.valueChanged[float].connect(self.onWdrcLargeSPLChanged)
            obj = self.findChild(QtWidgets.QDoubleSpinBox, 'dSBoxLargeSPLGainBand_%s' % idx)
            obj.valueChanged[float].connect(self.onWdrcLargeSPLGainChanged)

        self.ui.actionExport.triggered.connect(self.onExportConfigParams)
        self.ui.actionImport.triggered.connect(self.onImportConfigParams)
        self.ui.actionAbout.triggered.connect(self.showVersinInfo)
        self.ui.actionClearLocalDb.triggered.connect(self.onClearLocalRecords)
        self.ui.actionUploadRecords.triggered.connect(self.onUploadLocalRecords)
        self.ui.actionExportRecords.triggered.connect(self.onExportRecords)
        self.ui.actionStyleAuto.triggered.connect(self.onSetAutoStyle)
        self.ui.actionStyleLight.triggered.connect(self.onSetLightStyle)
        self.ui.actionStyleDark.triggered.connect(self.onSetDarkStyle)

    def setUISignalBlock(self, state):
        logging.info("Set UI Signal %s", "Blocked" if state else "non-Blocked")
        for btn in self.findChildren(QtWidgets.QPushButton):
            btn.blockSignals(state)
        for comboBox in self.findChildren(QtWidgets.QComboBox):
            comboBox.blockSignals(state)
        for checkBox in self.findChildren(QtWidgets.QCheckBox):
            checkBox.blockSignals(state)
        for spinBox in self.findChildren(QtWidgets.QDoubleSpinBox):
            spinBox.blockSignals(state)
        for spinBox in self.findChildren(QtWidgets.QSpinBox):
            spinBox.blockSignals(state)
        for lineEdit in self.findChildren(QtWidgets.QLineEdit):
            lineEdit.blockSignals(state)
        for slider in self.findChildren(QtWidgets.QSlider):
            slider.blockSignals(state)
        for radioBtn in self.findChildren(QtWidgets.QRadioButton):
            radioBtn.blockSignals(state)
        for groupBox in self.findChildren(QtWidgets.QGroupBox):
            groupBox.blockSignals(state)

    def setup_worker_connections(self):
        """设置工作线程信号连接"""
        self.aidsDev.eventLoopStarted.connect(self.on_event_loop_started)
        self.aidsDev.deviceFound.connect(self.add_device)
        self.aidsDev.scanComplete.connect(self.scan_complete)
        self.aidsDev.connectionStatus.connect(self.update_connection_status)
        self.aidsDev.characteristicUpdated.connect(self.aidsDev.handle_characteristic_update)
        self.aidsDev.servicesDiscovered.connect(self.update_services)

        # self.ble_worker.writeComplete.connect(self.handle_write_complete)
        # self.ble_worker.readComplete.connect(self.handle_read_complete)
        self.aidsDev.disconnectComplete.connect(self.handle_disconnect_complete)

    def onQApplicationStarted(self):
        logging.info("onQApplicationStarted!")
        self.bindEvent()
        QApplication.processEvents()
        if self.localRecordDb is None:
            self.logWrite("记录存储数据库初始化失败!", style="fail")
        # self.initRemoteDb()
        self.aidsDev.start()

        lastDevice = self.appConfig.get("Setting", "last_connect_dev", fallback='')
        if lastDevice:
            lastDevice = eval(lastDevice)
            self.known_devices[lastDevice["address"]] = lastDevice
            self.update_device_list()

    def createLogTextRightMenu(self):
        self.logTextMenu.popup(QtGui.QCursor.pos(), at=None)

    def saveSysConfig(self):
        ConfigParser.saveConfigFile(self.appConfig, "Config.ini")

    def clearLogText(self):
        self.ui.logText.clear()

    def logWrite(self, msg, style="default"):
        logging.info(msg)
        if style == "pass":
            text = "<font color=#0000ff size=4>%s</font>" % msg
        elif style == "fail":
            text = "<font color=red size=4>%s</font>" % msg
        else:
            text = "<font size=4>%s</font>" % msg
        self.ui.logText.append(text)
        QApplication.processEvents()

    def on_event_loop_started(self):
        pass

    def stopScan(self):
        self.aidsDev.enqueue_command("scan_stop", None)
        self.bleState = "idle"
        self.logWrite("停止扫描")
        self.ui.btnScan.setText("开始扫描")

    def scanDevices(self):
        if self.bleState == "scanning":
            self.stopScan()
            return
        logging.info("Start scanning devices...")
        """开始扫描设备"""
        self.ui.comboBoxDevList.clear()
        self.known_devices.clear()
        self.ui.btnScan.setText("停止扫描")
        self.ui.btnConnect.setEnabled(False)
        self.logWrite("开始扫描设备...")

        self.aidsDev.enqueue_command("scan_start", None)
        self.bleState = "scanning"

    def add_device(self, device):
        """添加或更新设备到列表"""
        device_info = {
            "name": device.name,
            "address": device.address,
            "rssi": device.rssi,
        }

        self.known_devices[device.address] = device_info
        self.update_device_list()

    def update_device_list(self):
        """更新设备列表显示"""
        self.ui.comboBoxDevList.clear()

        sorted_devices = sorted(
            self.known_devices.values(), key=lambda x: (x["rssi"] or -100), reverse=True
        )

        self.known_devices.clear()
        for device_info in sorted_devices:
            if not device_info["name"].upper().startswith("CASE") and not device_info["name"].upper().startswith("LEA"):
                continue
            rssi = device_info["rssi"] or 0
            item_text = f"{device_info['name']} ({device_info['address']})"
            self.known_devices[item_text] = device_info
            self.ui.comboBoxDevList.addItem(item_text)
            idx = self.ui.comboBoxDevList.findText(item_text)
            if idx != -1:
                self.ui.comboBoxDevList.setCurrentIndex(idx)

    def scan_complete(self):
        """扫描完成"""
        self.ui.btnScan.setEnabled(True)
        if (self.ui.comboBoxDevList.count() > 0):
            self.ui.btnConnect.setEnabled(True)
        self.logWrite(f"扫描完成，发现 {self.ui.comboBoxDevList.count()} 个设备")
        logging.info(self.known_devices)

    def connectDevice(self, item):
        if self.bleState == "connected":
            self.disconnect_device()
            return
        if not self.ui.comboBoxDevList.currentText():
            self.logWrite("请选择一个设备", style="fail")
            return
        device = self.known_devices[self.ui.comboBoxDevList.currentText()]
        self.logWrite(f"正在连接设备 {device['name']} ...")
        logging.info(f"Connecting to {device['name']}")
        self.connected_device = device
        self.aidsDev.enqueue_command("connect", device)
        self.ui.btnConnect.setEnabled(False)
        self.ui.btnScan.setEnabled(False)
        self.bleState = "connecting"

    def disconnect_device(self):
        """断开设备连接"""
        if not self.connected_device:
            self.ui.btnScan.setEnabled(True)
            self.logWrite("未连接到任何设备")
            return
        logging.info(f"Dis Connecting from {self.connected_device['name']}")
        self.aidsDev.enqueue_command("disconnect", None)
        self.bleState = "disconnecting"
        self.ui.btnConnect.setEnabled(False)
        self.ui.btnScan.setEnabled(False)
        self.ui.btnSync.setEnabled(False)
        self.ui.btnSave.setEnabled(False)

    def handle_disconnect_complete(self, success, message):
        """处理断开连接完成信号"""
        if success:
            if self.bleState != "disconnected":
                self.logWrite("已断开连接")
            self.aidsDev.devCharacteristicWrite = None
            self.aidsDev.devCharacteristicNotify = None
            self.bleState = "disconnected"
            self.connected_device = None
            self.current_char_handle = None
            self._is_subscribed = False
            self.ui.btnScan.setEnabled(True)
            self.ui.btnConnect.setEnabled(True)
            self.ui.btnConnect.setText("Connect")
            self.ui.btnSync.setEnabled(False)
            self.ui.btnSave.setEnabled(False)
            self.clearUI()
        else:
            self.logWrite(f"断开连接失败: {message}")
            self.ui.btnConnect.setEnabled(True)
            self.ui.btnSync.setEnabled(True)
            self.ui.btnSave.setEnabled(True)

    def update_connection_status(self, connected, message):
        """更新连接状态"""
        if connected:
            self.bleState = "connected"
            device_name = self.connected_device["name"]
            self.logWrite(f"已连接到 {device_name}", style="pass")
            self.ui.btnConnect.setEnabled(True)
            self.ui.btnConnect.setText("Disconnect")
            self.appConfig.set("Setting", "last_connect_dev", str(self.connected_device))
            self.saveSysConfig()
        else:
            self.logWrite(f"连接到失败: {message}", style="fail")
            self.connected_device = None
            self._is_subscribed = False
            self.ui.btnScan.setEnabled(True)
            self.ui.btnConnect.setEnabled(True)

    def update_services(self, services):
        """Update services and characteristics after connection"""
        if not self.aidsDev.update_services(services):
            self.logWrite("未找到指定的服务和特征值, 断开连接！", style="fail")
            self.disconnect_device()
            return
        self.logWrite("已订阅通知", style="pass")
        self.ui.btnSync.setEnabled(True)
        self.ui.btnSave.setEnabled(True)
        # Call syncParamsFromDevice to sync parameters from the device
        # self.syncParamsFromDevice()
        self.onSyncParams()

    def syncParamsFromDevice(self):
        if not self.aidsDev.devCharacteristicWrite or not self.aidsDev.devCharacteristicNotify:
            self.logWrite("BLE设备未连接！", style="fail")
            return

        # logging.info("Start to sync parameters from device.")
        # self.logWrite("正在同步参数 ...", style="pass")
        self.logWrite("正在获取WDRC中心频率 ...", style="pass")
        self.bleState = "syncing"

        # Disable UI controls
        self.setUISignalBlock(True)

        '''
        # 创建一个队列来存储所有需要执行的命令
        self.sync_commands = [
            "get_in_gain",
            "get_out_gain",
            "get_ns_mode",
            "get_wdrc_enable",
            "get_beam_enable",
            "get_beam_angle",
            "get_afc_mode",
            "get_afc_speech_level",
            "get_freqshift_enable"
        ]
        '''

        # Add WDRC center frequency commands for each band
        for band in range(self.bandNumber):
            self.sync_commands.append(f"wdrc_get_band_center_frequency|{band}")

        '''
        # Add WDRC band limit commands for each band
        for band in range(self.bandNumber):
            self.sync_commands.append(f"wdrc_get_band_limit|{band}")

        # Add WDRC dBSPL gain commands for each band for small, medium, and large SPL
        for band in range(self.bandNumber):
            smallSPLWidget = self.findChild(QtWidgets.QDoubleSpinBox, f'dSBoxSmallSPLBand_{band}')
            mediumSPLWidget = self.findChild(QtWidgets.QDoubleSpinBox, f'dSBoxMediumSPLBand_{band}')
            largeSPLWidget = self.findChild(QtWidgets.QDoubleSpinBox, f'dSBoxLargeSPLBand_{band}')
            if smallSPLWidget:
                smallSPL = round(smallSPLWidget.value() * 10)
                self.sync_commands.append(f"wdrc_get_band_dBSPL_gain|{band},{smallSPL}")
            if mediumSPLWidget:
                mediumSPL = round(mediumSPLWidget.value() * 10)
                self.sync_commands.append(f"wdrc_get_band_dBSPL_gain|{band},{mediumSPL}")
            if largeSPLWidget:
                largeSPL = round(largeSPLWidget.value() * 10)
                self.sync_commands.append(f"wdrc_get_band_dBSPL_gain|{band},{largeSPL}")
        '''

        # 创建一个工作线程来按顺序执行命令
        self.current_sync_worker = SyncParamsThread(self.aidsDev, self.sync_commands[0])
        self.current_sync_worker.result_signal.connect(self.handle_sync_result)
        self.current_sync_worker.finished.connect(self.handle_sync_worker_finished)

        # 保存当前命令索引
        self.current_sync_index = 0

        # 启动第一个命令
        self.current_sync_worker.start()

    def handle_sync_worker_finished(self):
        """处理同步工作线程完成的情况"""
        # 清理当前工作线程
        if self.current_sync_worker:
            self.current_sync_worker.deleteLater()
            self.current_sync_worker = None

        # 检查是否还有更多命令需要执行
        self.current_sync_index += 1
        if self.current_sync_index < len(self.sync_commands):
            # 创建新的工作线程执行下一个命令
            self.current_sync_worker = SyncParamsThread(
                self.aidsDev,
                self.sync_commands[self.current_sync_index]
            )
            self.current_sync_worker.result_signal.connect(self.handle_sync_result)
            self.current_sync_worker.finished.connect(self.handle_sync_worker_finished)
            self.current_sync_worker.start()
        else:
            # 所有命令执行完成
            # self.logWrite("参数同步完成", style="pass")
            self.logWrite("WDRC中心频率更新完成", style="pass")
            self.sync_commands = []
            self.current_sync_index = None
            # Enable UI controls
            self.setUISignalBlock(False)
            self.onSyncParams()
            self.bleState = "connected"

    @staticmethod
    def bytes_to_int32(byte_list):
        if len(byte_list) != 4:
            raise ValueError("需要4字节的列表")
        value = byte_list[0] | (byte_list[1] << 8) | (byte_list[2] << 16) | (byte_list[3] << 24)
        if value & 0x80000000:
            value = value - 0x100000000
        return value

    @staticmethod
    def int32_to_bytes(value):
        return [(value >> 0) & 0xFF, (value >> 8) & 0xFF, (value >> 16) & 0xFF, (value >> 24) & 0xFF]

    def handle_sync_result(self, cmdStr, state, response):
        """处理同步命令的执行结果"""
        if state:
            logging.info(f"Command {cmdStr} completed with response: {response}")
            if cmdStr == "get_in_gain":
                in_gain = self.bytes_to_int32(response)
                self.ui.doubleSpinBoxInputGain.setValue(in_gain / 10.0)
            elif cmdStr == "get_out_gain":
                out_gain = self.bytes_to_int32(response)
                self.ui.doubleSpinBoxOutputGain.setValue(out_gain / 10.0)
            elif cmdStr == "get_ns_mode":
                ns_mode = self.bytes_to_int32(response)
                logging.info(f"Get NS mode: {ns_mode}")
                if 0 <= ns_mode <= 9:
                    self.ui.comboBoxNsMode.setCurrentIndex(ns_mode)
                else:
                    self.logWrite(f"获取NS模式无效!", style="fail")
            elif cmdStr == "get_wdrc_enable":
                state_val = self.bytes_to_int32(response)
                self.ui.groupBoxWDRC.setChecked(state_val != 0)
            elif cmdStr == "get_beam_enable":
                state_val = self.bytes_to_int32(response)
                self.ui.groupBoxBeam.setChecked(state_val != 0)
            elif cmdStr == "get_beam_angle":
                angle = self.bytes_to_int32(response)
                logging.info(f"Get Beam angle: {angle}")
                self.ui.spinBoxBeamAngle.setValue(angle)
            elif cmdStr == "get_afc_mode":
                afc_mode = self.bytes_to_int32(response)
                logging.info(f"Get AFC mode: {afc_mode}")
                if afc_mode >= 0 and afc_mode <= 2:
                    self.ui.comboBoxAFCMode.setCurrentIndex(afc_mode)
                else:
                    self.logWrite(f"获取AFC模式无效!", style="fail")
            elif cmdStr == "get_afc_speech_level":
                level = self.bytes_to_int32(response) / 10.0
                logging.info(f"Get AFC speech level: {level}")
                self.ui.doubleSpinBoxAFCSpeechLevel.setValue(level)
            elif cmdStr == "get_freqshift_enable":
                state_val = self.bytes_to_int32(response)
                self.ui.groupBoxFreqShift.setChecked(state_val != 0)
            elif cmdStr.startswith("wdrc_get_band_center_frequency"):
                band_index = self.bytes_to_int32(response[:4])
                center_freq = self.bytes_to_int32(response[4:])
                logging.info(f"Get WDRC Band {band_index} center frequency: {center_freq}")
                if band_index >= 0 and band_index < self.bandNumber:
                    obj = self.findChild(QtWidgets.QGroupBox, f'gpBand_{band_index}')
                    obj.setTitle(f"Band {band_index}: {center_freq} Hz")
                else:
                    self.logWrite(f"获取WDRC带 {band_index} 中心频率无效!", style="fail")
            elif cmdStr.startswith("wdrc_get_band_limit"):
                band_index = self.bytes_to_int32(response[:4])
                threshold = self.bytes_to_int32(response[4:8])
                knee = self.bytes_to_int32(response[8:12])
                logging.info(f"Get WDRC Band {band_index} limit: threshold={threshold}, knee={knee}")
                threshold_widget = self.findChild(QtWidgets.QDoubleSpinBox, f"dSBoxLimitThresholdBand_{band_index}")
                knee_widget = self.findChild(QtWidgets.QDoubleSpinBox, f"dSBoxLimitKneeBand_{band_index}")
                if threshold_widget:
                    threshold_widget.setValue(threshold / 10.0)
                if knee_widget:
                    knee_widget.setValue(knee / 10.0)
            elif cmdStr.startswith("wdrc_get_band_dBSPL_gain"):
                band_index = self.bytes_to_int32(response[:4])
                spl = self.bytes_to_int32(response[4:8])
                gain = self.bytes_to_int32(response[8:])
                logging.info(f"Get WDRC Band {band_index} dBSPL:{spl} gain: {gain}")
                if band_index >= 0 and band_index < self.bandNumber:
                    smallSPLWidget = self.findChild(QtWidgets.QDoubleSpinBox, f'dSBoxSmallSPLBand_{band_index}')
                    mediumSPLWidget = self.findChild(QtWidgets.QDoubleSpinBox, f'dSBoxMediumSPLBand_{band_index}')
                    largeSPLWidget = self.findChild(QtWidgets.QDoubleSpinBox, f'dSBoxLargeSPLBand_{band_index}')

                    smallSPLGainWidget = self.findChild(QtWidgets.QDoubleSpinBox, f'dSBoxSmallSPLGainBand_{band_index}')
                    mediumSPLGainWidget = self.findChild(QtWidgets.QDoubleSpinBox, f'dSBoxMediumSPLGainBand_{band_index}')
                    largeSPLGainWidget = self.findChild(QtWidgets.QDoubleSpinBox, f'dSBoxLargeSPLGainBand_{band_index}')
                    if smallSPLWidget and smallSPLGainWidget:
                        if spl == int(round(smallSPLWidget.value() * 10)):
                            smallSPLGainWidget.setValue(gain / 10.0)
                    if mediumSPLWidget and mediumSPLGainWidget:
                        if spl == int(round(mediumSPLWidget.value() * 10)):
                            mediumSPLGainWidget.setValue(gain / 10.0)
                    if largeSPLWidget and largeSPLGainWidget:
                        if spl == int(round(largeSPLWidget.value() * 10)):
                            largeSPLGainWidget.setValue(gain / 10.0)
                else:
                    self.logWrite(f"获取WDRC带 {band_index} dBSPL增益无效!", style="fail")
            else:
                # ...existing code...
                pass
        else:
            logging.error(f"Command {cmdStr} failed!")
            self.logWrite(f"命令 {cmdStr} 执行失败！", style="fail")
            # 可以选择是否继续执行后续命令
            # self.current_sync_index = len(self.sync_commands)  # 停止后续命令执行

    def syncActivateState(self):
        activeState = self.aidsDev.getCryptState()
        logging.info(f"Get Crypt State: {activeState}")
        if activeState == 1:
            styleSheet = """QLineEdit { background-color: green; color: white }"""
            self.ui.leditAcivateState.setStyleSheet(styleSheet)
            self.ui.leditAcivateState.setText("设备已激活")
            self.logWrite("设备已激活！", style="pass")
            self.ui.btnAlgActivate.setEnabled(False)
            return True
        else:
            styleSheet = """QLineEdit { color: red }"""
            self.ui.leditAcivateState.setStyleSheet(styleSheet)
            self.ui.leditAcivateState.setText("设备未激活")
            self.logWrite("设备未激活！", style="fail")
            self.ui.btnAlgActivate.setEnabled(True)
            return False

    def clearUI(self):
        self.setUISignalBlock(True)
        self.ui.groupBoxYmAlg.setChecked(False)
        self.ui.doubleSpinBoxInputGain.setValue(0.0)
        self.ui.doubleSpinBoxOutputGain.setValue(0.0)
        self.ui.comboBoxNsMode.setCurrentIndex(0)
        self.ui.groupBoxWDRC.setChecked(False)
        self.ui.groupBoxBeam.setChecked(False)
        self.ui.spinBoxBeamAngle.setValue(0)
        self.ui.comboBoxAFCMode.setCurrentIndex(0)
        self.ui.doubleSpinBoxAFCSpeechLevel.setValue(0.0)
        self.ui.groupBoxFreqShift.setChecked(False)
        self.ui.groupBoxWDRC.setChecked(False)
        self.ui.leditVersionInfo.setText("")
        self.ui.leditAcivateState.setText("")
        self.ui.leditAcivateState.setStyleSheet("")
        self.ui.comboBoxDebugLevel.setCurrentIndex(4)
        self.ui.comboBoxAudioDumpFormat.setCurrentIndex(0)
        self.setUISignalBlock(False)

    def syncParamsWithUI(self, paramsCfg: CaseParams, blockUI=True):
        if blockUI:
            self.setUISignalBlock(True)

        self.ui.groupBoxYmAlg.setChecked(paramsCfg.ym_algo_state != 0)
        self.ui.doubleSpinBoxInputGain.setValue(paramsCfg.in_gain / 10.0)
        self.ui.doubleSpinBoxOutputGain.setValue(paramsCfg.out_gain / 10.0)
        self.ui.groupBoxNs.setChecked(paramsCfg.ns_state != 0)
        self.ui.comboBoxNsMode.setCurrentIndex(paramsCfg.ns_mode)
        self.ui.groupBoxBeam.setChecked(paramsCfg.beam_state!= 0)
        self.ui.spinBoxBeamAngle.setValue(paramsCfg.beam_angle)
        self.ui.groupBoxFreqShift.setChecked(paramsCfg.freqshift_state!= 0)
        self.ui.doubleSpinBoxFreqShiftLevel.setValue(paramsCfg.freqshift_level / 10.0)
        self.ui.groupBoxAFC.setChecked(paramsCfg.afc_state!= 0)
        self.ui.comboBoxAFCMode.setCurrentIndex(paramsCfg.afc_mode)
        self.ui.doubleSpinBoxAFCSpeechLevel.setValue(paramsCfg.afc_speech_level / 10.0)
        self.ui.spinBoxAFCDelay.setValue(paramsCfg.afc_delay)
        self.ui.spinBoxAFCFilterLen.setValue(paramsCfg.afc_filter_len)
        self.ui.groupBoxLimit.setChecked(paramsCfg.limit_state!= 0)
        self.ui.dSBoxLimitThreshold.setValue(paramsCfg.limit_threshold / 10.0)
        self.ui.dSBoxLimitKnee.setValue(paramsCfg.limit_knee / 10.0)
        self.ui.groupBoxWDRC.setChecked(paramsCfg.wdrc_cfg.wdrc_state!= 0)
        self.ui.sliderAfcAllGain.setValue(paramsCfg.wdrc_cfg.wdrc_all_band_gain)
        self.ui.spinAfcAllGain.setValue(paramsCfg.wdrc_cfg.wdrc_all_band_gain / 10.0)
        for i in range(self.bandNumber):
            bandGroupObj = self.findChild(QtWidgets.QGroupBox, f'gpBand_{i}')
            if bandGroupObj:
                bandGroupObj.setTitle(f"Band {i}: {paramsCfg.wdrc_cfg.band_cfg[i].frequency} Hz")
            attackObj = self.findChild(QtWidgets.QSpinBox, f"spinBoxAttackBand_{i}")
            if attackObj:
                logging.info(f"Band {i} attack time: {paramsCfg.wdrc_cfg.band_cfg[i].attack_time}")
                attackObj.setValue(paramsCfg.wdrc_cfg.band_cfg[i].attack_time)
            else:
                logging.info(f"Band {i} attack time not found!")
            releaseObj = self.findChild(QtWidgets.QSpinBox, f"spinBoxReleaseBand_{i}")
            if releaseObj:
                logging.info(f"Band {i} release time: {paramsCfg.wdrc_cfg.band_cfg[i].release_time}")
                releaseObj.setValue(paramsCfg.wdrc_cfg.band_cfg[i].release_time)
            else:
                logging.info(f"Band {i} release time not found!")
            limitThresholdObj = self.findChild(QtWidgets.QDoubleSpinBox, f"dSBoxLimitThresholdBand_{i}")
            if limitThresholdObj:
                limitThresholdObj.setValue(paramsCfg.wdrc_cfg.band_cfg[i].limit_threshold / 10.0)
            limitKneeObj = self.findChild(QtWidgets.QDoubleSpinBox, f"dSBoxLimitKneeBand_{i}")
            if limitKneeObj:
                limitKneeObj.setValue(paramsCfg.wdrc_cfg.band_cfg[i].limit_knee / 10.0)
            smallSPLObj = self.findChild(QtWidgets.QDoubleSpinBox, f"dSBoxSmallSPLBand_{i}")
            if smallSPLObj:
                smallSPLObj.setValue(paramsCfg.wdrc_cfg.band_cfg[i].small_spl / 10.0)
            smallSPLGainObj = self.findChild(QtWidgets.QDoubleSpinBox, f"dSBoxSmallSPLGainBand_{i}")
            if smallSPLGainObj:
                smallSPLGainObj.setValue(paramsCfg.wdrc_cfg.band_cfg[i].small_spl_gain / 10.0)
            mediumSPLObj = self.findChild(QtWidgets.QDoubleSpinBox, f"dSBoxMediumSPLBand_{i}")
            if mediumSPLObj:
                mediumSPLObj.setValue(paramsCfg.wdrc_cfg.band_cfg[i].medium_spl / 10.0)
            mediumSPLGainObj = self.findChild(QtWidgets.QDoubleSpinBox, f"dSBoxMediumSPLGainBand_{i}")
            if mediumSPLGainObj:
                mediumSPLGainObj.setValue(paramsCfg.wdrc_cfg.band_cfg[i].medium_spl_gain / 10.0)
            largeSPLObj = self.findChild(QtWidgets.QDoubleSpinBox, f"dSBoxLargeSPLBand_{i}")
            if largeSPLObj:
                largeSPLObj.setValue(paramsCfg.wdrc_cfg.band_cfg[i].large_spl / 10.0)
            largeSPLGainObj = self.findChild(QtWidgets.QDoubleSpinBox, f"dSBoxLargeSPLGainBand_{i}")
            if largeSPLGainObj:
                largeSPLGainObj.setValue(paramsCfg.wdrc_cfg.band_cfg[i].large_spl_gain / 10.0)

        if blockUI:
            self.setUISignalBlock(False)

    def checkPsapSwState(self):
        cmdStr = "get_psap_sw_state"
        state, response = self.aidsDev.send_command(cmdStr, params=None, blocking=True)
        if state and response and len(response) == 4:
            state_val = self.bytes_to_int32(response)
            logging.info(f"Get PSAP SW state: {state_val}")
            if state_val in [0, 1]:
                self.logWrite(f"PSAP SW: {state_val}", style="pass")
                self.ui.comboBoxPsapSwCtrl.blockSignals(True)
                self.ui.comboBoxPsapSwCtrl.setCurrentIndex(state_val)
                self.ui.comboBoxPsapSwCtrl.blockSignals(False)
                return True
            else:
                self.logWrite(f"PSAP SW: {state_val}", style="fail")
                return False
        else:
            logging.error(f"Command {cmdStr} failed!")
            self.logWrite(f"获取 PSAP_SW 状态失败！", style="fail")
            return False
        
    def checkSweepFreqState(self):
        resData = self.aidsDev.getSweepFreqState()
        if resData:
            sweepState = self.bytes_to_int32(resData[:4])
            sweepFreq = self.bytes_to_int32(resData[4:])
            self.ui.groupBoxSweepFreq.blockSignals(True)
            self.ui.groupBoxSweepFreq.setChecked(sweepState != 0)
            self.ui.groupBoxSweepFreq.blockSignals(False)
            self.ui.spinBoxSweepFreq.blockSignals(True)
            self.ui.spinBoxSweepFreq.setValue(sweepFreq)
            self.ui.spinBoxSweepFreq.blockSignals(False)
            self.ui.comboBoxSweepType.blockSignals(True)
            self.ui.comboBoxSweepType.setCurrentIndex(sweepState)
            self.ui.comboBoxSweepType.blockSignals(False)
            return True
        else:
            logging.error("获取扫频状态失败！")
            return False

    def showVersionInfo(self):
        versionInfo = ""
        version = self.aidsDev.getHifiVersionInfo()
        if version:
            verStr = str(bytes(version), encoding="utf-8")
            logging.info(f"Hifi版本信息: {verStr}")
            ver = verStr.split("|")
            if len(ver) == 4:
                logging.info(f"HIFI4 固件版本: {ver[0]}")
                logging.info(f"Build: {ver[1]}")
                logging.info(f"Git Commit: {ver[2]}")
                logging.info(f"lib: {ver[3].split(': ')[-1].strip()}")
                sub_version = re.search(r'\bv\d+\.\d+\b', ver[3])  # 匹配v开头+数字+.数字的格式
                lib_version = sub_version.group(0) if sub_version else ver[3].split(': ')[-1].strip()

                versionInfo = f"HIFI4: V{ver[0]}, Build: {ver[1]}, lib: {lib_version}"
            else:
                logging.error("HIFI4 固件版本信息格式错误")
                return False
        else:
            self.logWrite(f"获取Hifi版本信息失败！", style="fail")
            return False
        version = self.aidsDev.getM55VersionInfo()
        if version:
            verStr = str(bytes(version), encoding="utf-8")
            logging.info(f"M55版本信息: {verStr.split(': ')[-1].strip()}")
            sub_version = re.search(r'\bv\d+\.\d+\b', verStr)
            m55_version = sub_version.group(0) if sub_version else verStr.split(': ')[-1].strip()
            versionInfo += f", M55 lib: {m55_version}"
        else:
            self.logWrite(f"获取M55版本信息失败！", style="fail")
            return False

        self.ui.leditVersionInfo.setText(versionInfo)
        return True

    def onSyncParams(self):
        self.logWrite("正在同步参数 ...", style="pass")

        if not self.checkPsapSwState():
            return

        if self.ui.comboBoxPsapSwCtrl.currentText() == "OFF":
            self.logWrite("PSAP SW 未开启，无法同步参数！", style="fail")
            return

        if not self.showVersionInfo():
            return
        
        if not self.checkSweepFreqState():
            self.logWrite("获取扫频状态失败！", style="fail")

        AidsParamsSize = ctypes.sizeof(CaseParams)
        complete_data = bytearray(AidsParamsSize)
        # 每次请求的数据块大小
        chunkSize = 192
        # 分块检索数据
        for offset in range(0, AidsParamsSize, chunkSize):
            # 计算本次请求的字节数
            length = min(chunkSize, AidsParamsSize - offset)
            # 发送命令
            cmdStr = f"get_ym_cfg|{offset},{length}"
            state, response = self.aidsDev.send_command(cmdStr, params=None, blocking=True)
            if state and self.bytes_to_int32(response[:4]) == 0 and len(response[4:]) == length:
                # 合并数据:
                # logging.info("response: %s", response)
                complete_data[offset:offset + len(response[4:])] = response[4:]
                logging.info(f"成功获取位置 {offset} 的数据块，长度 {len(response[4:])}")
            else:
                logging.error(f"命令 {cmdStr} 失败!")
                self.logWrite(f"同步参数失败！", style="fail")
                return

        # 解析完整数据
        paramsCfg = CaseParams.from_buffer_copy(complete_data)
        # showAidsParams(paramsCfg)
        self.syncParamsWithUI(paramsCfg)
        self.devConfigParams = structure_to_dict(paramsCfg)
        self.syncActivateState()
        self.logWrite("参数同步完成", style="pass")

    def onSaveParams(self):
        if not self.aidsDev.devCharacteristicWrite or not self.aidsDev.devCharacteristicNotify:
            self.logWrite("BLE设备未连接！", style="fail")
            return

        logging.info("Save YM DSP params.")
        saveType = 0
        cmdStr = f"save_ym_cfg|{saveType}"
        state, response = self.aidsDev.send_command(cmdStr, params=None, blocking=True)
        if state:
            self.logWrite("参数保存成功！", style="pass")
        else:
            self.logWrite("参数保存失败！", style="fail")

    def onYmAlgEnabled(self):
        if not self.aidsDev.devCharacteristicWrite or not self.aidsDev.devCharacteristicNotify:
            self.logWrite("BLE设备未连接！", style="fail")
            return

        if self.ui.groupBoxYmAlg.isChecked():
            self.logWrite("启用助听器算法")
            state = 1
        else:
            self.logWrite("禁用助听器算法")
            state = 0

        cmdStr = f"ym_algo|{state}"
        self.aidsDev.send_command(cmdStr, params=None, blocking=True)

    def onInputGainChanged(self, value):
        if not self.aidsDev.devCharacteristicWrite or not self.aidsDev.devCharacteristicNotify:
            self.logWrite("BLE设备未连接！", style="fail")
            return
        self.logWrite(f"设置输入增益为 {value} dB")
        cmdStr = f"set_in_gain|{int(value * 10)}"
        self.aidsDev.send_command(cmdStr, params=None, blocking=True)

    def onOutputGainChanged(self, value):
        if not self.aidsDev.devCharacteristicWrite or not self.aidsDev.devCharacteristicNotify:
            self.logWrite("BLE设备未连接！", style="fail")
            return
        self.logWrite(f"设置输出增益为 {value} dB")
        cmdStr = f"set_out_gain|{int(value * 10)}"
        self.aidsDev.send_command(cmdStr, params=None, blocking=True)

    def onNsEnabled(self):
        if not self.aidsDev.devCharacteristicWrite or not self.aidsDev.devCharacteristicNotify:
            self.logWrite("BLE设备未连接！", style="fail")
            return

        if self.ui.groupBoxNs.isChecked():
            self.logWrite("启用噪声抑制")
            state = 1
        else:
            self.logWrite("禁用噪声抑制")
            state = 0

        cmdStr = f"set_ns_enable|{state}"
        self.aidsDev.send_command(cmdStr, params=None, blocking=True)

    def onNsModeChanged(self, index):
        if not self.aidsDev.devCharacteristicWrite or not self.aidsDev.devCharacteristicNotify:
            self.logWrite("BLE设备未连接！", style="fail")
            return

        self.logWrite(f"设置噪声抑制模式为 {index}")
        cmdStr = f"set_ns_mode|{index}"
        self.aidsDev.send_command(cmdStr, params=None, blocking=True)

    def onAfcEnabled(self):
        if not self.aidsDev.devCharacteristicWrite or not self.aidsDev.devCharacteristicNotify:
            self.logWrite("BLE设备未连接！", style="fail")
            return

        if self.ui.groupBoxAFC.isChecked():
            self.logWrite("启用自适应反馈消除")
            state = 1
        else:
            self.logWrite("禁用自适应反馈消除")
            state = 0

        cmdStr = f"set_afc_enable|{state}"
        self.aidsDev.send_command(cmdStr, params=None, blocking=True)

    def onAfcModeChanged(self, index):
        if not self.aidsDev.devCharacteristicWrite or not self.aidsDev.devCharacteristicNotify:
            self.logWrite("BLE设备未连接！", style="fail")
            return

        self.logWrite(f"设置自适应反馈消除模式为 {index}")
        cmdStr = f"set_afc_mode|{index}"
        self.aidsDev.send_command(cmdStr, params=None, blocking=True)
        # state, response = self.aidsDev.send_command("get_afc_mode", params=None, blocking=True)
        # logging.info("%s, %s", state, response)

    def onAfcSpeechLevelChanged(self, value):
        if not self.aidsDev.devCharacteristicWrite or not self.aidsDev.devCharacteristicNotify:
            self.logWrite("BLE设备未连接！", style="fail")
            return

        self.logWrite(f"设置自适应反馈消除语音电平为 {value}")
        cmdStr = f"set_afc_speech_level|{int(value * 10)}"
        self.aidsDev.send_command(cmdStr, params=None, blocking=True)

    def onAfcDelayChanged(self, value):
        if not self.aidsDev.devCharacteristicWrite or not self.aidsDev.devCharacteristicNotify:
            self.logWrite("BLE设备未连接！", style="fail")
            return

        self.logWrite(f"设置自适应反馈消除延迟为 {value}")
        cmdStr = f"set_afc_delay|{int(value)}"
        self.aidsDev.send_command(cmdStr, params=None, blocking=True)

    def onAfcFilterLenChanged(self, value):
        if not self.aidsDev.devCharacteristicWrite or not self.aidsDev.devCharacteristicNotify:
            self.logWrite("BLE设备未连接！", style="fail")
            return

        self.logWrite(f"设置自适应反馈消除滤波器长度为 {value}")
        cmdStr = f"set_afc_filter_len|{int(value)}"
        self.aidsDev.send_command(cmdStr, params=None, blocking=True)

    def onBeamEnabled(self):
        if not self.aidsDev.devCharacteristicWrite or not self.aidsDev.devCharacteristicNotify:
            self.logWrite("BLE设备未连接！", style="fail")
            return

        if self.ui.groupBoxBeam.isChecked():
            self.logWrite("启用波束成形")
            state = 1
        else:
            self.logWrite("禁用波束成形")
            state = 0

        cmdStr = f"set_beam_enable|{state}"
        self.aidsDev.send_command(cmdStr, params=None, blocking=True)

    def onBeamAngleChanged(self, value):
        if not self.aidsDev.devCharacteristicWrite or not self.aidsDev.devCharacteristicNotify:
            self.logWrite("BLE设备未连接！", style="fail")
            return

        self.logWrite(f"设置波束成形角度为 {value}")
        cmdStr = f"set_beam_angle|{int(value)}"
        self.aidsDev.send_command(cmdStr, params=None, blocking=True)

    def onFreqShiftEnabled(self):
        if not self.aidsDev.devCharacteristicWrite or not self.aidsDev.devCharacteristicNotify:
            self.logWrite("BLE设备未连接！", style="fail")
            return

        if self.ui.groupBoxFreqShift.isChecked():
            self.logWrite("启用频移")
            state = 1
        else:
            self.logWrite("禁用频移")
            state = 0

        cmdStr = f"set_freqshift_enable|{state}"
        self.aidsDev.send_command(cmdStr, params=None, blocking=True)

    def onFreqShiftLevelChanged(self, value):
        if not self.aidsDev.devCharacteristicWrite or not self.aidsDev.devCharacteristicNotify:
            self.logWrite("BLE设备未连接！", style="fail")
            return

        self.logWrite(f"设置频移电平为 {value}")
        cmdStr = f"set_freqshift_freqlevel|{int(value * 10)}"
        self.aidsDev.send_command(cmdStr, params=None, blocking=True)

    def onLimitEnabled(self):
        if not self.aidsDev.devCharacteristicWrite or not self.aidsDev.devCharacteristicNotify:
            self.logWrite("BLE设备未连接！", style="fail")
            return

        if self.ui.groupBoxLimit.isChecked():
            self.logWrite("启用Limit")
            limit_state = 0x01
        else:
            self.logWrite("禁用Limit")
            limit_state = 0x00
        cmdStr = f"set_limit_enable|{limit_state}"
        self.aidsDev.send_command(cmdStr, params=None, blocking=True)
        cmdStr = f"get_limit_enable"
        state, response = self.aidsDev.send_command(cmdStr, params=None, blocking=True)
        # logging.info("%s, %s", state, response)
        if state and len(response) == 4:
            if limit_state != self.bytes_to_int32(response):
                self.logWrite("Limit状态设置失败", style="fail")
        else:
            self.logWrite("Limit状态获取失败", style="fail")

    def onLimitThresholdChanged(self, value):
        if not self.aidsDev.devCharacteristicWrite or not self.aidsDev.devCharacteristicNotify:
            self.logWrite("BLE设备未连接！", style="fail")
            return

        self.logWrite(f"设置Limit Threshold: {value}")
        threshold = round(self.ui.dSBoxLimitThreshold.value() * 10)
        knee = round(self.ui.dSBoxLimitKnee.value() * 10)
        cmdStr = f"set_limit_threshold|{threshold},{knee}"
        self.aidsDev.send_command(cmdStr, params=None, blocking=True)
        cmdStr = f"get_limit_threshold"
        state, response = self.aidsDev.send_command(cmdStr, params=None, blocking=True)
        logging.info("%s, %s", state, response)
        if state and len(response) == 8:
            limit_threshold = self.bytes_to_int32(response[:4])
            limit_knee = self.bytes_to_int32(response[4:])
            logging.info("Get Limit Threshold: %d, Knee: %d", limit_threshold, limit_knee)
            if limit_threshold != threshold or limit_knee != knee:
                self.logWrite("Limit Threshold设置失败", style="fail")
        else:
            self.logWrite("Limit Threshold获取失败", style="fail")

    def onLimitKneeChanged(self, value):
        if not self.aidsDev.devCharacteristicWrite or not self.aidsDev.devCharacteristicNotify:
            self.logWrite("BLE设备未连接！", style="fail")
            return

        self.logWrite(f"设置Limit Knee: {value}")
        threshold = round(self.ui.dSBoxLimitThreshold.value() * 10)
        knee = round(self.ui.dSBoxLimitKnee.value() * 10)
        cmdStr = f"set_limit_threshold|{threshold},{knee}"
        self.aidsDev.send_command(cmdStr, params=None, blocking=True)
        cmdStr = f"get_limit_threshold"
        state, response = self.aidsDev.send_command(cmdStr, params=None, blocking=True)
        logging.info("%s, %s", state, response)
        if state and len(response) == 8:
            limit_threshold = self.bytes_to_int32(response[:4])
            limit_knee = self.bytes_to_int32(response[4:])
            logging.info("Get Limit Threshold: %d, Knee: %d", limit_threshold, limit_knee)
        else:
            logging.error("Get Limit Threshold failed")

    def onWdrcEnabled(self):
        if not self.aidsDev.devCharacteristicWrite or not self.aidsDev.devCharacteristicNotify:
            self.logWrite("BLE设备未连接！", style="fail")
            return

        if self.ui.groupBoxWDRC.isChecked():
            self.logWrite("启用WDRC")
            state = 1
        else:
            self.logWrite("禁用WDRC")
            state = 0

        cmdStr = f"set_wdrc_enable|{state}"
        self.aidsDev.send_command(cmdStr, params=None, blocking=True)

    def onSliderWdrcAllGainChanged(self, value):
        if not self.aidsDev.devCharacteristicWrite or not self.aidsDev.devCharacteristicNotify:
            self.logWrite("BLE设备未连接！", style="fail")
            return

        self.logWrite(f"设置WDRC增益为 {value / 10.0} dB")
        self.ui.spinAfcAllGain.setValue(value / 10.0)
        cmdStr = f"wdrc_set_all_band_gain|{int(value)}"
        self.aidsDev.send_command(cmdStr, params=None, blocking=True)

    def onSpinWdrcAllGainChanged(self, value):
        if not self.aidsDev.devCharacteristicWrite or not self.aidsDev.devCharacteristicNotify:
            self.logWrite("BLE设备未连接！", style="fail")
            return

        logging.debug(f"Spinbox设置WDRC增益为 {value} dB")
        self.ui.sliderAfcAllGain.setValue(round(value * 10))

    def onWdrcAttackChanged(self, value: int):
        if not self.aidsDev.devCharacteristicWrite or not self.aidsDev.devCharacteristicNotify:
            self.logWrite("BLE设备未连接！", style="fail")
            return
        sender = self.sender()
        band_index = int(sender.objectName().split("_")[-1]) if sender else 0
        self.logWrite(f"设置WDRC Attack Band {band_index} 为 {value}")
        cmdStr = f"wdrc_set_band_attack_time|{band_index},{value}"
        self.aidsDev.send_command(cmdStr, params=None, blocking=True)

    def onWdrcReleaseChanged(self, value: int):
        if not self.aidsDev.devCharacteristicWrite or not self.aidsDev.devCharacteristicNotify:
            self.logWrite("BLE设备未连接！", style="fail")
            return
        sender = self.sender()
        band_index = int(sender.objectName().split("_")[-1]) if sender else 0
        self.logWrite(f"设置WDRC Release Band {band_index} 为 {value}")
        cmdStr = f"wdrc_set_band_release_time|{band_index},{value}"
        self.aidsDev.send_command(cmdStr, params=None, blocking=True)

    def onWdrcLimitThresholdChanged(self, value: float):
        if not self.aidsDev.devCharacteristicWrite or not self.aidsDev.devCharacteristicNotify:
            self.logWrite("BLE设备未连接！", style="fail")
            return
        sender = self.sender()
        band_index = int(sender.objectName().split("_")[-1]) if sender else 0
        self.logWrite(f"设置WDRC Limit Threshold Band {band_index} 为 {value}")
        threshold = round(value * 10)
        knee = round(self.findChild(QtWidgets.QDoubleSpinBox, f"dSBoxLimitKneeBand_{band_index}").value() * 10)
        paramList = self.int32_to_bytes(band_index) + self.int32_to_bytes(threshold) + self.int32_to_bytes(knee)
        cmdStr = f"wdrc_set_band_limit|{band_index},{threshold},{knee}"
        self.aidsDev.send_command(cmdStr, params=None, blocking=True)

    def onWdrcLimitKneeChanged(self, value: float):
        if not self.aidsDev.devCharacteristicWrite or not self.aidsDev.devCharacteristicNotify:
            self.logWrite("BLE设备未连接！", style="fail")
            return
        sender = self.sender()
        band_index = int(sender.objectName().split("_")[-1]) if sender else 0
        self.logWrite(f"设置WDRC Limit Knee Band {band_index} 为 {value}")
        threshold = round(self.findChild(QtWidgets.QDoubleSpinBox, f"dSBoxLimitThresholdBand_{band_index}").value() * 10)
        knee = round(value * 10)
        cmdStr = f"wdrc_set_band_limit|{band_index},{threshold},{knee}"
        self.aidsDev.send_command(cmdStr, params=None, blocking=True)

    def setWdrcBandSPLGain(self, band_index: int):
        smallSPL = self.findChild(QtWidgets.QDoubleSpinBox, f"dSBoxSmallSPLBand_{band_index}").value()
        smallSPLGain = self.findChild(QtWidgets.QDoubleSpinBox, f"dSBoxSmallSPLGainBand_{band_index}").value()
        mediumSPL = self.findChild(QtWidgets.QDoubleSpinBox, f"dSBoxMediumSPLBand_{band_index}").value()
        mediumSPLGain = self.findChild(QtWidgets.QDoubleSpinBox, f"dSBoxMediumSPLGainBand_{band_index}").value()
        largeSPL = self.findChild(QtWidgets.QDoubleSpinBox, f"dSBoxLargeSPLBand_{band_index}").value()
        largeSPLGain = self.findChild(QtWidgets.QDoubleSpinBox, f"dSBoxLargeSPLGainBand_{band_index}").value()

        paramList = self.int32_to_bytes(band_index) + \
                    self.int32_to_bytes(round(smallSPL * 10)) + self.int32_to_bytes(round(smallSPLGain * 10)) + \
                    self.int32_to_bytes(round(mediumSPL * 10)) + self.int32_to_bytes(round(mediumSPLGain * 10)) + \
                    self.int32_to_bytes(round(largeSPL * 10)) + self.int32_to_bytes(round(largeSPLGain * 10))
        cmdStr = f"wdrc_set_band_dBSPL_gain|{band_index},{round(smallSPL * 10)},{round(smallSPLGain * 10)},{round(mediumSPL * 10)},{round(mediumSPLGain * 10)},{round(largeSPL * 10)},{round(largeSPLGain * 10)}"
        self.aidsDev.send_command(cmdStr, params=None, blocking=True)

    def onWdrcSmallSPLChanged(self, value: float):
        if not self.aidsDev.devCharacteristicWrite or not self.aidsDev.devCharacteristicNotify:
            self.logWrite("BLE设备未连接！", style="fail")
            return
        sender = self.sender()
        band_index = int(sender.objectName().split("_")[-1]) if sender else 0
        self.logWrite(f"设置WDRC Small SPL Band {band_index} 为 {value}")
        self.setWdrcBandSPLGain(band_index)

    def onWdrcSmallSPLGainChanged(self, value: float):
        if not self.aidsDev.devCharacteristicWrite or not self.aidsDev.devCharacteristicNotify:
            self.logWrite("BLE设备未连接！", style="fail")
            return
        sender = self.sender()
        band_index = int(sender.objectName().split("_")[-1]) if sender else 0
        self.logWrite(f"设置WDRC Small SPL Gain Band {band_index} 为 {value}")
        self.setWdrcBandSPLGain(band_index)

    def onWdrcMediumSPLChanged(self, value: float):
        if not self.aidsDev.devCharacteristicWrite or not self.aidsDev.devCharacteristicNotify:
            self.logWrite("BLE设备未连接！", style="fail")
            return
        sender = self.sender()
        band_index = int(sender.objectName().split("_")[-1]) if sender else 0
        self.logWrite(f"设置WDRC Medium SPL Band {band_index} 为 {value}")
        self.setWdrcBandSPLGain(band_index)

    def onWdrcMediumSPLGainChanged(self, value: float):
        if not self.aidsDev.devCharacteristicWrite or not self.aidsDev.devCharacteristicNotify:
            self.logWrite("BLE设备未连接！", style="fail")
            return
        sender = self.sender()
        band_index = int(sender.objectName().split("_")[-1]) if sender else 0
        self.logWrite(f"设置WDRC Medium SPL Gain Band {band_index} 为 {value}")
        self.setWdrcBandSPLGain(band_index)

    def onWdrcLargeSPLChanged(self, value: float):
        if not self.aidsDev.devCharacteristicWrite or not self.aidsDev.devCharacteristicNotify:
            self.logWrite("BLE设备未连接！", style="fail")
            return
        sender = self.sender()
        band_index = int(sender.objectName().split("_")[-1]) if sender else 0
        self.logWrite(f"设置WDRC Large SPL Band {band_index} 为 {value}")
        self.setWdrcBandSPLGain(band_index)

    def onWdrcLargeSPLGainChanged(self, value: float):
        if not self.aidsDev.devCharacteristicWrite or not self.aidsDev.devCharacteristicNotify:
            self.logWrite("BLE设备未连接！", style="fail")
            return
        sender = self.sender()
        band_index = int(sender.objectName().split("_")[-1]) if sender else 0
        self.logWrite(f"设置WDRC Large SPL Gain Band {band_index} 为 {value}")
        self.setWdrcBandSPLGain(band_index)

    def onPsapSwCtrlChanged(self, index):
        if not self.aidsDev.devCharacteristicWrite or not self.aidsDev.devCharacteristicNotify:
            self.logWrite("BLE设备未连接！", style="fail")
            return
        self.logWrite(f"设置PSAP开关为 {self.ui.comboBoxPsapSwCtrl.currentText()}")
        cmdStr = f"psap_sw|{index}"
        self.aidsDev.send_command(cmdStr, params=None, blocking=True)

    def onDebugLevelChanged(self, index):
        if not self.aidsDev.devCharacteristicWrite or not self.aidsDev.devCharacteristicNotify:
            self.logWrite("BLE设备未连接！", style="fail")
            return

        logLevel = index
        dump = self.ui.comboBoxAudioDumpFormat.currentIndex()
        self.logWrite(f"设置调试级别为 {logLevel}, Audio Dump: {dump}")

        cmdStr = f"bth_log_level|{logLevel},{dump}"
        self.aidsDev.send_command(cmdStr, params=None, blocking=True)

    def onAudioDumpChanged(self, index):
        if not self.aidsDev.devCharacteristicWrite or not self.aidsDev.devCharacteristicNotify:
            self.logWrite("BLE设备未连接！", style="fail")
            return

        logLevel = self.ui.comboBoxDebugLevel.currentIndex()
        dump = index
        self.logWrite(f"设置调试级别为 {logLevel}, Audio Dump: {dump}")

        cmdStr = f"bth_log_level|{logLevel},{dump}"
        self.aidsDev.send_command(cmdStr, params=None, blocking=True)

    def onSweepFreqEnabled(self):
        if not self.aidsDev.devCharacteristicWrite or not self.aidsDev.devCharacteristicNotify:
            self.logWrite("BLE设备未连接！", style="fail")
            return
        
        if self.ui.groupBoxSweepFreq.isChecked():
            self.logWrite("启用扫频")
            state = 1
        else:
            self.logWrite("禁用扫频")
            state = 0
        freq = self.ui.spinBoxSweepFreq.value()
        sweepType = self.ui.comboBoxSweepType.currentIndex()
        if state == 0:
            sweepType = 0

        if not self.aidsDev.setSweepFreqState(self.int32_to_bytes(sweepType), self.int32_to_bytes(freq)):
            self.logWrite("设置扫频状态失败！", style="fail")
        else:
            self.logWrite(f"设置扫频状态为 {sweepType}, 频率为: {freq}")
        resData = self.aidsDev.getSweepFreqState()
        if resData:
            sweepState = self.bytes_to_int32(resData[:4])
            sweepFreq = self.bytes_to_int32(resData[4:])
            logging.info(f"扫频状态为 {sweepState}, 扫频频率为 {sweepFreq}")
        else:
            logging.error("获取扫频状态失败！")

    def onSweepFreqChanged(self, value):
        if not self.aidsDev.devCharacteristicWrite or not self.aidsDev.devCharacteristicNotify:
            self.logWrite("BLE设备未连接！", style="fail")
            return
        
        self.logWrite(f"设置扫频频率为 {value}")
        sweepType = self.ui.comboBoxSweepType.currentIndex()
        freq = value
        self.aidsDev.setSweepFreqState(self.int32_to_bytes(sweepType), self.int32_to_bytes(freq))
        resData = self.aidsDev.getSweepFreqState()
        if resData:
            sweepState = self.bytes_to_int32(resData[:4])
            sweepFreq = self.bytes_to_int32(resData[4:])
            logging.info(f"扫频状态为 {sweepState}, 扫频频率为 {sweepFreq}")
        else:
            logging.error("获取扫频状态失败！")

    def onSweepTypeChanged(self, index):
        if not self.aidsDev.devCharacteristicWrite or not self.aidsDev.devCharacteristicNotify:
            self.logWrite("BLE设备未连接！", style="fail")
            return
        self.logWrite(f"设置扫频类型为 {index}")
        sweepType = index
        freq = self.ui.spinBoxSweepFreq.value()
        self.aidsDev.setSweepFreqState(self.int32_to_bytes(sweepType), self.int32_to_bytes(freq))
        resData = self.aidsDev.getSweepFreqState()
        if resData:
            sweepState = self.bytes_to_int32(resData[:4])
            sweepFreq = self.bytes_to_int32(resData[4:])
            logging.info(f"扫频状态为 {sweepState}, 扫频频率为 {sweepFreq}")

    def onRmsTimerFunc(self):
        cmdStr = f"get_frame_rms"
        state, response = self.aidsDev.send_command(cmdStr, params=None, blocking=True)
        logging.info("%s, %s", state, response)
        if state and len(response) == 8:
            rms = self.bytes_to_int32(response[:4])
            low_freq_rms = self.bytes_to_int32(response[4:])
            logging.info("Get Frame RMS: %d, Low Frequency RMS: %d", rms, low_freq_rms)
            self.ui.leditRMSVal.setText(f"{rms / 10.0} dB")
            self.ui.leditRMSVal_LF.setText(f"{low_freq_rms / 10.0} dB")
        else:
            logging.error("获取帧RMS检测失败")

    def onFrameRmsEnabled(self):
        if not self.aidsDev.devCharacteristicWrite or not self.aidsDev.devCharacteristicNotify:
            self.logWrite("BLE设备未连接！", style="fail")
            return
        if self.ui.groupBoxFrameRms.isChecked():
            self.logWrite("启用帧RMS检测")
            if not self.rmsTimer.isActive():
                self.rmsTimer.start(1000)
        else:
            self.logWrite("禁用帧RMS检测")
            if self.rmsTimer.isActive():
                self.rmsTimer.stop()

    def onAlgActivate(self):
        if not self.aidsDev.devCharacteristicWrite or not self.aidsDev.devCharacteristicNotify:
            self.logWrite("BLE设备未连接！", style="fail")
            return

        logging.info("Start to activate device.")
        activeState = self.aidsDev.getCryptState()
        if activeState == 1:
            self.logWrite("设备已激活！", style="fail")
            return

        # Get flash UID
        flashUID = self.aidsDev.getFlashUID()
        if flashUID:
            str_flash_uid = "".join(f"{b:02x}" for b in flashUID)
            logging.info(f"Get Flash UID: {str_flash_uid}")
        else:
            logging.error(f"Get Flash UID failed")
            return
        
        ret, cryptData = self.getActivateAuthKey(flashUID)
        if not ret:
            self.logWrite("激活授权失败！", style="fail")
            return

        # Set crypto key
        if not self.aidsDev.setCryptoKey(cryptData=cryptData):
            logging.error("设置加密密钥失败！")
            return
        else:
            logging.info("设置加密密钥成功！")

        # check auth state
        self.syncActivateState()

    def getActivateAuth(self):
        serverIP = self.appConfig.get("Server", "ip")
        serverPort = self.appConfig.get("Server", "port")
        loginUser = self.appConfig.get("Server", "user")
        loginPasswd = self.appConfig.get("Server", "passwd")
        devName = self.appConfig.get("Server", "model")

        logging.info("Start to get Activate Authorization, device: %s", devName)
        server = ServerRequest(serverIP, serverPort)
        ret, returnData = server.userLogin(loginUser, loginPasswd)
        if not ret:
            logging.error("%s login error: %s" % (loginUser, returnData))
            return False

        ret, devData = server.getDeviceInfo(devName)
        if not ret:
            logging.error("Get device info error: %s" % returnData)
            server.userLogout()
            return False

        leftNum = devData["left_num"]
        if leftNum <= 0:
            self.logWrite("授权数量不足！", style="fail")
            return False
        action = {"type": "ask_auth_num", "num": 1, "device": devName}
        ret, jsonData = server.addRequestRecord(action)
        if not ret:
            self.logWrite("请求授权失败！", style="fail")
            server.userLogout()
            return False

        server.userLogout()

        logging.info("Get Activate Authorization Success!")

        return True

    def getActivateAuthKey(self, rawIdData):
        serverIP = self.appConfig.get("Server", "ip")
        serverPort = self.appConfig.get("Server", "port")
        loginUser = self.appConfig.get("Server", "user")
        loginPasswd = self.appConfig.get("Server", "passwd")
        devName = self.appConfig.get("Server", "model")
        cryptData = None

        logging.info("Start to get Activate Authorization Key, device: %s", devName)
        server = ServerRequest(serverIP, serverPort)
        ret, returnData = server.userLogin(loginUser, loginPasswd)
        if not ret:
            logging.error("%s login error: %s" % (loginUser, returnData))
            return False, cryptData

        ret, devData = server.getDeviceInfo(devName)
        if not ret:
            logging.error("Get device info error: %s" % returnData)
            server.userLogout()
            return False, cryptData

        leftNum = devData["left_num"]
        if leftNum <= 0:
            self.logWrite("授权数量不足！", style="fail")
            return False, cryptData
        action = {"type": "gen_auth_key", "num": 1, "device": devName, "rawId": rawIdData}
        ret, jsonData = server.addRequestRecord(action)
        if not ret:
            self.logWrite("请求授权失败！", style="fail")
            server.userLogout()
            return False, cryptData
        
        try:
            action_data = eval(jsonData["action"])
            if not isinstance(action_data, dict) or "cryptData" not in action_data:
                logging.error("Invalid action data format")
                server.userLogout()
                return False, cryptData
            
            cryptData = action_data["cryptData"]
        except Exception as e:
            logging.error(f"Failed to parse action data: {e}")
            server.userLogout()
            return False, cryptData

        server.userLogout()

        logging.info("Get Activate Authorization Success!")

        return True, cryptData

    def setTestResult(self, ledit, msg, style="default"):
        if style == "pass":
            styleSheet = """QLineEdit { background-color: green; color: white }"""
        elif style == "fail":
            styleSheet = """QLineEdit { background-color: red; color: black }"""
        elif style == "warning":
            styleSheet = """QLineEdit { background-color: yellow; color: black }"""
        else:
            styleSheet = """QLineEdit { background-color: white; color: black }"""
            # styleSheet = None

        if styleSheet:
            ledit.setStyleSheet(styleSheet)
        ledit.setText(msg)
        QApplication.processEvents()

    def openConfigWin(self):
        """
        logging.info('Show User Login Window.')
        self.login.exec()
        if self.login.cancel is True:
            logging.info('User cancel login.')
            return
        if self.login.srvHandle is None:
            return
        """

        logging.info("Open Advanced setting window.")
        # self.setting.srvHandle = self.login.srvHandle
        self.saveSysConfig()

        # self.login.srvHandle.userLogout()
        # self.login.srvHandle = None

    def onExportConfigParams(self):
        logging.info("Export Config Params.")
        if not self.devConfigParams:
            self.showMessage('参数没有同步!', msgType=QtWidgets.QMessageBox.Icon.Warning)
            return

        filename = QtWidgets.QFileDialog.getSaveFileName(
            self, "导出配置参数", "", "Config File (*.json);;all files(*.*)"
        )
        if filename[0] != "":
            logging.info("Export config params to %s", filename[0])
            ConfigParser.saveJsonFile(self.devConfigParams, filename[0])
            self.showMessage('导出参数成功！', msgType=QtWidgets.QMessageBox.Icon.Information)
            
    
    def onImportConfigParams(self):
        logging.info("Import Config Params.")
        if not self.aidsDev.devCharacteristicWrite or not self.aidsDev.devCharacteristicNotify:
            self.showMessage('助听器设备未连接!', msgType=QtWidgets.QMessageBox.Icon.Warning)
            return

        filename = QtWidgets.QFileDialog.getOpenFileName(
            self, "导入配置参数", "", "Config File (*.json);;all files(*.*)"
        )
        if filename[0] != "":
            logging.info("Import config params from %s", filename[0])
            jsonData = ConfigParser.loadJsonFile(filename[0])
            if jsonData:
                self.devConfigParams = jsonData
                paramsCfg = json_to_aids_params(jsonData)
                # showAidsParams(paramsCfg)
                self.syncParamsWithUI(paramsCfg, blockUI=False)
                self.showMessage('导入参数成功！', msgType=QtWidgets.QMessageBox.Icon.Information)
            else:
                self.showMessage('导入参数失败！', msgType=QtWidgets.QMessageBox.Icon.Warning)
    
    def onExportRecords(self):
        logging.info("Export test Records.")
        if not self.localRecordDb:
            self.logWrite("本地测试记录数据库错误！", style="fail")
            return

        filename = QtWidgets.QFileDialog.getSaveFileName(
            self, "导出本地测试记录", "", "Record File (*.csv);;all files(*.*)"
        )
        if filename[0] != "":
            logging.info("Export records to %s", filename[0])
            records = self.localRecordDb.getAllRecords(
                1, self.appConfig.get("Setting", "step_name")
            )
            header = self.localRecordDb.colList
            CsvFileOps.saveDataToCsvFile(records, header, filename[0])

            self.showMessage(
                "导出本地测试记录成功！", msgType=QtWidgets.QMessageBox.Icon.Information
            )

    def onClearLocalRecords(self):
        reply = self.showMessage(
            "确定要清除本地测试记录？",
            msgType=QtWidgets.QMessageBox.Icon.Question,
            btnNo=True,
        )
        if reply == QtWidgets.QMessageBox.StandardButton.Yes:
            logging.info("Start to clear local records.")
            if self.localRecordDb.clearRecords():
                self.logWrite("清除本地测试数据成功！", style="pass")
            else:
                self.logWrite("清除本地测试数据失败！", style="fail")
        else:
            logging.info("Cancel clear local records.")

    def onUploadLocalRecords(self):
        if not self.remoteRecordDb:
            self.remoteRecordDb = DevTestMysqlDb()
            self.remoteRecordDb.initDatabase()

        if not self.remoteRecordDb.checkConnection():
            self.showMessage(
                "远程数据库未连接！", msgType=QtWidgets.QMessageBox.Icon.Warning
            )
            self.remoteRecordDb = None
            return False
        else:
            if self.leditRemoteDb:
                self.setTestResult(self.leditRemoteDb, "连接正常", "pass")
            self.remoteDbConnect = True

        if not self.localRecordDb:
            self.logWrite("本地测试记录数据库错误！", style="fail")
            return False

        logging.info("Start to upload local records.")
        records = self.localRecordDb.getAllRecords(
            2, self.appConfig.get("Setting", "step_name"), omitID=False
        )
        header = ["id"] + self.localRecordDb.colList
        passCnt = 0
        failCnt = 0
        for record in records:
            data = dict(zip(header, record))
            if "UPLOAD" in data and data["UPLOAD"] != "1":
                # logging.info('upload record: %s', data)
                recordId = data["id"]
                del data["UPLOAD"]
                del data["id"]
                if self.remoteRecordDb.checkAndUploadRecordData(data):
                    # logging.info('upload record success!')
                    passCnt = passCnt + 1
                    self.localRecordDb.updateUploadState(recordId, "1")
                else:
                    failCnt = failCnt + 1
                    self.localRecordDb.updateUploadState(recordId, "0")

        logging.info("Upload finished, success: %d, fail: %d", passCnt, failCnt)
        self.showMessage(
            "上传记录完成，成功：%d 失败：%d！" % (passCnt, failCnt),
            msgType=QtWidgets.QMessageBox.Icon.Information,
        )

    def onSetAutoStyle(self):
        logging.info("Set Auto Style.")
        qdarktheme.setup_theme("auto")

    def onSetLightStyle(self):
        logging.info("Set Light Style.")
        qdarktheme.setup_theme("light")

    def onSetDarkStyle(self):
        logging.info("Set Dark Style.")
        qdarktheme.setup_theme("dark")

    def showVersinInfo(self):
        logging.info("Show Version Info: %s", VERSION_STR)
        msg = "<p align='center'>%s</p>" % self.windowTitle()
        self.showMessage(
            msg,
            msgType=QtWidgets.QMessageBox.Icon.Information,
            btnYes=False,
            btnNo=False,
        )

    def closeEvent(self, event):
        logging.info("Audio analysis tool closed.")
        reply = self.showMessage(
            "关闭软件？", msgType=QtWidgets.QMessageBox.Icon.Question, btnNo=True
        )
        if reply == QtWidgets.QMessageBox.StandardButton.Yes:
            # self.saveSysConfig()
            try:
                if self.aidsDev:
                    if self.aidsDev.client and self.aidsDev.client.is_connected:
                        # 先断开连接
                        self.aidsDev.enqueue_command("disconnect", None)
                        # 等待断开操作完成，可以增加一个事件来通知断开完成
                        self.aidsDev.wait(2000)
                    # 先尝试正常关闭
                    self.aidsDev.handle_close()
                    # 等待线程结束
                    if not self.aidsDev.wait(3000):  # 增加等待时间到3秒
                        logging.warning(
                            "BLE工作线程未能在规定时间内正常退出，准备强制终止"
                        )
                        # 如果等待超时，强制终止线程
                        self.aidsDev.terminate()
                        # 再次等待一小段时间确保线程被终止
                        self.aidsDev.wait(1000)
            except Exception as e:
                logging.error(f"关闭窗口时出错: {str(e)}")
            finally:
                event.accept()  # 确保窗口始终能够关闭
        else:
            event.ignore()


if __name__ == "__main__":
    app = QApplication()
    qdarktheme.setup_theme("auto")
    appMain = CaseCtrlTool()
    appMain.show()
    sys.exit(app.exec())
